const express = require("express");
const { authenticate, authorize } = require("../middleware/authMiddleware");
const { addProduct, updateProduct, deleteProduct, getProducts, scanBarcode } = require("../controllers/productController");
const { processGooglePay, processApplePay, processCryptoPayment, confirmPayment, generateReceipt } = require("../controllers/paymentController");
const upload = require("../config/multerConfig");
const multer = require('multer');
const xlsx = require('xlsx');
const { parse } = require('csv-parse');
const Product = require('../models/Product');
const fs = require('fs');

const router = express.Router();

// Configure multer for file uploads
const uploadMulter = multer({
  dest: 'uploads/',
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || 
        file.mimetype === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
        file.mimetype === 'application/vnd.ms-excel') {
      cb(null, true);
    } else {
      cb(new Error('Invalid file type. Please upload CSV or Excel file.'));
    }
  }
});

// Add a new product with image upload
router.post("/", authenticate, upload.single("image"), addProduct);

// Update a product with image upload
router.put("/:id", authenticate, upload.single("image"), updateProduct);

// Delete a product
router.delete("/:id", authenticate, deleteProduct);

// Get products for a specific store
router.get("/:storeId", authenticate, getProducts);

// Barcode scanning endpoint
router.post("/scan", scanBarcode);

// Payment endpoints
router.post("/payment/google_pay", authenticate, processGooglePay);
router.post("/payment/apple_pay", authenticate, processApplePay);
router.post("/payment/crypto", authenticate, processCryptoPayment);
router.get("/payment/status/:transactionId", confirmPayment);
router.post("/receipt", generateReceipt);

// Import products from file
router.post('/import', authenticate, uploadMulter.single('file'), async (req, res) => {
  if (!req.file) {
    return res.status(400).json({ message: 'No file uploaded' });
  }

  try {
    const storeId = req.body.storeId;

    // Verify store access
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied. Can only import to your own store." });
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (req.file.size > maxSize) {
      fs.unlinkSync(req.file.path); // Clean up
      return res.status(400).json({ message: 'File size exceeds 5MB limit' });
    }

    let products = [];
    const fileExt = req.file.originalname.split('.').pop().toLowerCase();

    // Validate file type
    if (!['csv', 'xlsx', 'xls'].includes(fileExt)) {
      fs.unlinkSync(req.file.path); // Clean up
      return res.status(400).json({
        message: 'Invalid file type. Only CSV and Excel files are supported.'
      });
    }

    if (fileExt === 'csv') {
      // Parse CSV with better error handling
      const fileContent = fs.readFileSync(req.file.path, 'utf-8');
      products = await new Promise((resolve, reject) => {
        parse(fileContent, {
          columns: true,
          skip_empty_lines: true,
          trim: true
        }, (err, records) => {
          if (err) {
            reject(new Error(`CSV parsing error: ${err.message}`));
          } else {
            resolve(records);
          }
        });
      });
    } else {
      // Parse Excel with error handling
      try {
        const workbook = xlsx.readFile(req.file.path);
        const sheetName = workbook.SheetNames[0];

        if (!sheetName) {
          throw new Error('No worksheets found in Excel file');
        }

        const worksheet = workbook.Sheets[sheetName];
        products = xlsx.utils.sheet_to_json(worksheet);
      } catch (error) {
        throw new Error(`Excel parsing error: ${error.message}`);
      }
    }

    // Validate minimum data requirements
    if (!products || products.length === 0) {
      fs.unlinkSync(req.file.path); // Clean up
      return res.status(400).json({ message: 'No valid data found in file' });
    }

    // Validate and format products with detailed error tracking
    const formattedProducts = [];
    const errors = [];

    products.forEach((product, index) => {
      const rowNumber = index + 2; // Account for header row
      const rowErrors = [];

      // Validate barcode
      if (!product.barcode || product.barcode.toString().trim() === '') {
        rowErrors.push('Missing barcode');
      }

      // Validate name
      if (!product.name || product.name.toString().trim() === '') {
        rowErrors.push('Missing product name');
      }

      // Validate price
      const price = parseFloat(product.price);
      if (isNaN(price) || price <= 0) {
        rowErrors.push('Invalid price (must be a positive number)');
      }

      if (rowErrors.length > 0) {
        errors.push(`Row ${rowNumber}: ${rowErrors.join(', ')}`);
      } else {
        formattedProducts.push({
          barcode: product.barcode.toString().trim(),
          name: product.name.toString().trim(),
          price: price,
          description: (product.description || '').toString().trim(),
          storeId: storeId
        });
      }
    });

    // If there are validation errors, return them
    if (errors.length > 0) {
      fs.unlinkSync(req.file.path); // Clean up
      return res.status(400).json({
        message: 'Validation errors found',
        errors: errors.slice(0, 10), // Limit to first 10 errors
        totalErrors: errors.length
      });
    }

    // Check for duplicate barcodes within the file
    const barcodes = formattedProducts.map(p => p.barcode);
    const duplicateBarcodes = barcodes.filter((barcode, index) => barcodes.indexOf(barcode) !== index);

    if (duplicateBarcodes.length > 0) {
      fs.unlinkSync(req.file.path); // Clean up
      return res.status(400).json({
        message: 'Duplicate barcodes found in file',
        duplicates: [...new Set(duplicateBarcodes)]
      });
    }

    // Bulk upsert products with better error handling
    const operations = formattedProducts.map(product => ({
      updateOne: {
        filter: { barcode: product.barcode, storeId: storeId },
        update: { $set: product },
        upsert: true
      }
    }));

    const result = await Product.bulkWrite(operations, { ordered: false });

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json({
      success: true,
      message: 'Products imported successfully',
      summary: {
        totalProcessed: formattedProducts.length,
        inserted: result.upsertedCount,
        updated: result.modifiedCount,
        errors: result.writeErrors?.length || 0
      },
      details: {
        created: result.upsertedCount,
        updated: result.modifiedCount
      }
    });

  } catch (error) {
    // Clean up uploaded file in case of error
    if (req.file && fs.existsSync(req.file.path)) {
      fs.unlinkSync(req.file.path);
    }

    console.error('Product import error:', error);
    res.status(400).json({
      message: 'Failed to import products',
      error: error.message
    });
  }
});

// Create template endpoint
router.get('/template', authenticate, (req, res) => {
  // Create a workbook with template structure
  const workbook = xlsx.utils.book_new();
  const templateData = [
    {
      barcode: '123456789',
      name: 'Example Product',
      price: '99.99',
      description: 'Product description'
    }
  ];

  const worksheet = xlsx.utils.json_to_sheet(templateData);
  xlsx.utils.book_append_sheet(workbook, worksheet, 'Products');

  // Set response headers
  res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
  res.setHeader('Content-Disposition', 'attachment; filename=products_template.xlsx');

  // Write workbook to response
  const buffer = xlsx.write(workbook, { type: 'buffer', bookType: 'xlsx' });
  res.send(buffer);
});

module.exports = router;